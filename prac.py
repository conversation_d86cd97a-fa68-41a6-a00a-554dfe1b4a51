import numpy as np

# Original problematic code (creates reference, not copy)
print("=== PROBLEM: Reference vs Copy ===")
A = np.array([[2, 4], [6, 8]])
B = A  # This creates a REFERENCE to the same array!
B[0,0] = 10
print("A:", A)  # Both A and B will be modified!
print("B:", B)
print("A is B:", A is B)  # True - they're the same object
print()

# SOLUTION: Use .copy() to create independent arrays
print("=== SOLUTION: Using .copy() ===")
A = np.array([[2, 4], [6, 8]])  # Reset A
B = A.copy()  # Create an independent copy
B[0,0] = 10
print("A:", A)  # A remains unchanged: [[2, 4], [6, 8]]
print("B:", B)  # B is modified: [[10, 4], [6, 8]]
print("A is B:", A is B)  # False - they're different objects
print()

# Alternative methods for copying:
print("=== OTHER COPY METHODS ===")
A = np.array([[2, 4], [6, 8]])
B1 = np.copy(A)        # Using np.copy()
B2 = np.array(A)       # Creating new array from existing
B3 = A[:]              # Slice copy (shallow copy)

B1[0,0] = 100
B2[0,1] = 200
B3[1,0] = 300

print("Original A:", A)
print("B1 (np.copy):", B1)
print("B2 (np.array):", B2)
print("B3 (slice):", B3)