import random
import math

# ============================================================
# PART 1 — BASIC PYTHON OBJECT TYPES
# ============================================================

"""
KEY DATA STRUCTURES IN PYTHON:

1. STRING (str) - Immutable sequence of characters
   - Why important: Text processing, user input/output, data parsing
   - Operations: indexing, slicing, concatenation, searching, formatting
   - Time complexity: O(1) for access, O(n) for search, O(n) for concatenation
   - Memory: Immutable means new string created for modifications

2. LIST (list) - Mutable, ordered collection
   - Why important: Dynamic arrays, most versatile data structure
   - Operations: append O(1), insert O(n), delete O(n), access O(1), search O(n)
   - Memory: Contiguous memory, automatic resizing when capacity exceeded
   - Use when: Need ordered, changeable collection with duplicates allowed

3. TUPLE (tuple) - Immutable, ordered collection
   - Why important: Data integrity, hashable (can be dict keys), memory efficient
   - Operations: access O(1), search O(n), no modification operations
   - Memory: More memory efficient than lists, stored in contiguous memory
   - Use when: Fixed data that shouldn't change (coordinates, RGB values)

4. SET (set) - Mutable, unordered collection of unique elements
   - Why important: Fast membership testing, mathematical set operations
   - Operations: add O(1), remove O(1), membership test O(1), union/intersection
   - Memory: Hash table implementation, no duplicates stored
   - Use when: Need unique elements, fast lookups, set mathematics

5. DICTIONARY (dict) - Mutable, unordered key-value pairs
   - Why important: Fast lookups by key, data mapping, caching
   - Operations: access O(1), insert O(1), delete O(1), search by key O(1)
   - Memory: Hash table with separate chaining for collisions
   - Use when: Need key-based access, mapping relationships, fast lookups
"""

# String, List, Tuple, Set, Dict examples
my_string = "I am Groot!"              # Immutable sequence - can't change individual characters
my_list = ["I", "am", "Groot!"]        # Mutable sequence - can modify, append, remove elements
my_tuple = ("I", "am", "Groot!")       # Immutable sequence - faster than list, can be dict key
my_set = {"I", "am", "Groot!"}         # Unordered unique elements - fast membership testing
my_dict = {"catchphrase1": "I am Groot!", "catchphrase2": "Please stop"}  # Key-value mapping

print("\n--- Data Types ---")
print(type(my_string), type(my_list), type(my_tuple), type(my_set), type(my_dict))

# Try accessing elements
print("\nList index 2:", my_list[2])
print("Tuple slice:", my_tuple[:2])

# Sets don’t support indexing
# print(my_set[0]) # <-- try this, you'll get an error
# DICTIONARY ACCESS OPERATIONS:
print("Dict access by key:", my_dict["catchphrase1"])  # O(1) average case lookup
print("Dict keys:", list(my_dict.keys()))              # Get all keys as list
print("Dict values:", list(my_dict.values()))          # Get all values as list

# SET OPERATIONS (mathematical set theory):
print("Set membership test:", "I" in my_set)           # O(1) average case - very fast!
print("Set union:", my_set | {"We", "are"})           # Combine sets, remove duplicates
print("Set intersection:", my_set & {"I", "We"})      # Common elements only

# ============================================================
# PART 2 — STRING SLICING PRACTICE
# ============================================================

"""
STRING SLICING - POWERFUL TEXT MANIPULATION:
- Syntax: string[start:stop:step]
- Why important: Extract substrings, reverse text, pattern extraction
- Time complexity: O(k) where k is length of slice
- Memory: Creates new string object (strings are immutable)
- Negative indices: Count from end (-1 is last character)
- Step parameter: Skip characters (2 = every other, -1 = reverse)
"""

a = "fascinating as the first 89 times you told me that!"

print("\n--- String Slicing ---")
print(a[1:5])        # 'asci' - characters at indices 1,2,3,4 (stop is exclusive)
print(a[2:9:3])      # 'sni' - start at 2, stop before 9, step by 3
print(a[-1:-6:-1])   # '!taht' - reverse slice from end, negative step goes backwards
print(a[7:11])       # 'ting' - simple slice, characters 7-10

# ADVANCED SLICING TECHNIQUES:
print(a[::2])        # Every 2nd character - step through entire string
print(a[::-1])       # Reverse entire string - common Python idiom
print(a[10:])        # From index 10 to end - omit stop parameter
print(a[:10])        # From start to index 10 - omit start parameter

# ============================================================
# PART 3 — TRUTHY / FALSY
# ============================================================

"""
BOOLEAN EVALUATION IN PYTHON:
- Why important: Control flow, conditional logic, data validation
- Falsy values: False, None, 0, 0.0, "", [], {}, set()
- Truthy values: Everything else (True, non-zero numbers, non-empty containers)
- Use cases: Input validation, default values, conditional execution
- Performance: bool() conversion is O(1) for most types
"""

print("\n--- Truthy / Falsy ---")
test_values = [0, 1, "", "False", [], [0], {}, {"a":1}]
for val in test_values:
    # repr() shows exact representation, bool() converts to True/False
    print(f"{repr(val):>8} -> {bool(val)}")

# PRACTICAL APPLICATIONS:
# 1. Input validation
user_input = ""
if not user_input:  # Empty string is falsy
    print("Please provide input!")

# 2. Default values using 'or' operator
name = user_input or "Anonymous"  # Use "Anonymous" if user_input is falsy

# 3. Checking if containers have data
data_list = []
if data_list:  # Empty list is falsy
    print("Processing data...")
else:
    print("No data to process")

# ============================================================
# PART 4 — LIST METHODS PRACTICE
# ============================================================

"""
LIST OPERATIONS - DYNAMIC ARRAY MANIPULATION:
- append(x): Add element to end - O(1) amortized time
- insert(i, x): Insert at position i - O(n) time (shifts elements)
- remove(x): Remove first occurrence - O(n) time (searches then shifts)
- pop(): Remove last element - O(1) time
- pop(i): Remove element at index i - O(n) time
- sort(): In-place sorting - O(n log n) time, stable sort
- reverse(): In-place reversal - O(n) time
- extend(iterable): Add all elements from iterable - O(k) where k is length

Why lists are important:
- Dynamic sizing (unlike arrays in C/Java)
- Heterogeneous data (can store different types)
- Rich set of built-in methods
- Memory efficient for sequential access
"""

print("\n--- List Practice ---")
test = ["i", "am", "groot"]
test.append("a")        # O(1) - add to end, most efficient insertion
test.append("b")        # O(1) - amortized constant time
test.append("a")        # O(1) - duplicates allowed in lists
test.remove("a")        # O(n) - removes the FIRST 'a' only, searches from start
print("After append/remove:", test)

test.sort()             # O(n log n) - in-place Timsort, stable algorithm
print("After sort:", test)  # Uppercase < lowercase in ASCII values

test.pop()              # O(1) - removes and returns last item
print("After pop:", test)

test.reverse()          # O(n) - reverses list in-place
print("After reverse:", test)

# ADDITIONAL USEFUL LIST OPERATIONS:
test.extend(["we", "are"])      # O(k) - more efficient than multiple appends
print("After extend:", test)
print("Index of 'am':", test.index("am"))  # O(n) - find first occurrence
print("Count of 'groot':", test.count("groot"))  # O(n) - count occurrences

# ============================================================
# PART 5 — DEFAULT FUNCTION ARGUMENTS & PITFALLS
# ============================================================

"""
MUTABLE DEFAULT ARGUMENTS - COMMON PYTHON PITFALL:
- Why dangerous: Default values are evaluated only once at function definition
- Problem: Mutable objects (lists, dicts, sets) are shared between calls
- Memory: Same object reference used across all function calls
- Solution: Use None as default, create new object inside function
- Best practice: Never use mutable objects as default arguments
"""

print("\n--- Mutable Default Args ---")

def mystery(x, items=[]):  # DANGEROUS! List created once at function definition
    items.append(x)        # Modifies the SAME list object every time
    row = [[0]*x]          # Creates new list each time (this is fine)
    return row, items

a, b = mystery(1)  # items = [1]
c, d = mystery(2)  # items = [1, 2] - SAME list object!
e, f = mystery(3)  # items = [1, 2, 3] - SAME list object!

print(a, b)  # [[0], [1, 2, 3]] - unexpected behavior!
print(c, d)  # [[0, 0], [1, 2, 3]] - all share same list
print(e, f)  # [[0, 0, 0], [1, 2, 3]]

# Why are they all sharing the same 'items'?
# Because Python creates 'items' only once when defining the function!
# The default value is bound to the function object, not created per call

# CORRECT SOLUTION:
def mystery_fixed(x, items=None):  # Use None as sentinel value
    if items is None:              # Check if no argument provided
        items = []                 # Create NEW list for each call
    items.append(x)
    row = [[0]*x]
    return row, items

print("\n--- Fixed version ---")
print(mystery_fixed(1))  # Creates new list [1]
print(mystery_fixed(2))  # Creates new list [2]
print(mystery_fixed(3))  # Creates new list [3]

# ============================================================
# PART 6 — FUNCTION ARGUMENTS (*args, **kwargs)
# ============================================================

"""
VARIABLE ARGUMENTS - FLEXIBLE FUNCTION INTERFACES:
- *args: Collects positional arguments into tuple
- **kwargs: Collects keyword arguments into dictionary
- Why important: Create flexible APIs, wrapper functions, decorators
- Order matters: def func(pos, *args, keyword=default, **kwargs)
- Unpacking: Use * and ** to unpack sequences/mappings when calling

Use cases:
- Functions that accept variable number of arguments
- Wrapper functions that pass arguments to other functions
- Decorators that need to preserve function signatures
"""

print("\n--- *args and **kwargs ---")

def product(*nums, scale=1):  # *nums collects positional args into tuple
    """Multiplies any number of arguments, then scales the result"""
    p = scale
    for n in nums:            # Iterate through tuple of arguments
        p *= n
    return p

# FUNCTION CALL EXAMPLES:
print(product(3, 2, 20))      # 120 - nums=(3,2,20), scale=1
print(product(1, 2, 1))       # 2 - nums=(1,2,1), scale=1
print(product([2,1], 2))      # [2,1,2,1] - list repetition! nums=([2,1],2)
print(product(3, 2, scale=10)) # 60 - nums=(3,2), scale=10

# INVALID SYNTAX EXAMPLES:
# print(product(scale=10, 2, 1)) # Error: positional args after keyword args

# ADVANCED EXAMPLE WITH BOTH *args AND **kwargs:
def flexible_func(required, *args, default=None, **kwargs):
    """Demonstrates all argument types"""
    print(f"Required: {required}")
    print(f"Args: {args}")
    print(f"Default: {default}")
    print(f"Kwargs: {kwargs}")

flexible_func("hello", 1, 2, 3, default="world", extra="data", more=True)

# ============================================================
# PART 7 — MINI CLASS PRACTICE (simplified myMatrix)
# ============================================================

"""
OBJECT-ORIENTED PROGRAMMING - CLASSES AND OBJECTS:
- Why important: Code organization, data encapsulation, reusability
- __init__: Constructor method, initializes object state
- __str__: String representation for print() and str()
- __add__, __gt__, __lt__: Operator overloading (magic methods)
- self: Reference to current instance
- Attributes: Data stored in object (self.n, self.data)
- Methods: Functions that operate on object data

Key OOP concepts:
- Encapsulation: Bundle data and methods together
- Abstraction: Hide implementation details, expose interface
- Code reuse: Create multiple instances with same behavior
"""

print("\n--- Simple Matrix Class ---")

class MiniMatrix:
    def __init__(self, n=3, letters=('a','b','c')):
        """Constructor: Initialize matrix with random letters"""
        # Instance attributes - each object has its own copy
        self.n = n  # Matrix dimension (n×n)
        # List comprehension: creates 2D array filled with random letters
        self.data = [[random.choice(letters) for _ in range(n)] for _ in range(n)]

    def __str__(self):
        """String representation - called by print() and str()"""
        # Join rows with newlines, join elements in each row with spaces
        return "\n".join(" ".join(row) for row in self.data)

    def one_norm(self):
        """Compute the 1-norm (max column sum) - mathematical operation"""
        max_sum = 0
        for j in range(self.n):  # For each column
            # Convert letters to numbers (a=0, b=1, ..., z=25)
            col_sum = sum(ord(self.data[i][j]) - 97 for i in range(self.n))
            max_sum = max(max_sum, col_sum)
        return max_sum

    def __add__(self, other):
        """Operator overloading: enables matrix1 + matrix2 syntax"""
        new = MiniMatrix(self.n)  # Create new matrix for result
        for i in range(self.n):
            for j in range(self.n):
                # Convert letters to numbers, add them, wrap around alphabet
                v1 = ord(self.data[i][j]) - 97  # Convert to 0-25
                v2 = ord(other.data[i][j]) - 97
                new.data[i][j] = chr((v1 + v2) % 26 + 97)  # Wrap and convert back
        return new

    def __gt__(self, other):
        """Greater than operator: matrix1 > matrix2"""
        return self.one_norm() > other.one_norm()

    def __lt__(self, other):
        """Less than operator: matrix1 < matrix2"""
        return self.one_norm() < other.one_norm()

# OBJECT INSTANTIATION AND USAGE:
m1 = MiniMatrix(3)          # Create first matrix object (calls __init__)
m2 = MiniMatrix(3)          # Create second matrix object (independent data)
print("Matrix 1:\n", m1)    # Calls __str__ method automatically
print("Matrix 2:\n", m2)    # Each object has its own data
print("m1 + m2:\n", m1 + m2)  # Calls __add__ method, returns new matrix
print("m1 > m2 ?", m1 > m2)   # Calls __gt__ method, compares norms

# ============================================================
# PART 8 — SMALL CALCULATOR PRACTICE
# ============================================================

"""
STRING PROCESSING AND CONTROL FLOW:
- split(): Breaks string into list of tokens - O(n) time
- Type conversion: float() converts string to number
- Error handling: Check for division by zero, invalid input
- Conditional logic: if/elif/else for different operations
- String formatting: Return appropriate error messages

Key programming concepts:
- Input validation (check token count)
- Error handling (division by zero)
- Type conversion (string to float)
- Control flow (conditional statements)
"""

print("\n--- Simple Calculator ---")

def calculator(expression):
    """Evaluate basic 3-token math expression like '3 + 2'"""
    tokens = expression.split()  # Split on whitespace into list
    if len(tokens) != 3:         # Validate input format
        return "Format error: use like '3 + 2'"

    # Type conversion with potential ValueError (not handled here)
    a = float(tokens[0])  # Convert first token to number
    op = tokens[1]        # Operator as string
    b = float(tokens[2])  # Convert third token to number

    # CONTROL FLOW: Check operator and perform calculation
    if op == '+':
        return a + b
    elif op == '-':
        return a - b
    elif op == '*':
        return a * b
    elif op == '/':
        return a / b if b != 0 else "Division by zero!"  # Error handling
    elif op == '^':
        return a ** b  # Exponentiation
    else:
        return "Invalid operator"

# FUNCTION TESTING:
print(calculator("2 + 3"))    # 5.0
print(calculator("5 ^ 2"))    # 25.0
print(calculator("5 / 0"))    # "Division by zero!"

# ============================================================
# PART 9 — PICKLING PRACTICE
# ============================================================

"""
OBJECT SERIALIZATION - PICKLING:
- Why important: Save Python objects to disk, send over network
- pickle.dump(): Serialize object to binary file
- pickle.load(): Deserialize object from binary file
- File modes: 'wb' (write binary), 'rb' (read binary)
- Context managers: 'with' statement ensures file is closed
- Limitations: Python-specific, security risks with untrusted data

Use cases:
- Caching computed results
- Saving program state
- Inter-process communication
- Machine learning model persistence

Security warning: Never unpickle data from untrusted sources!
"""

print("\n--- Pickling ---")
import pickle  # Note: imports should typically be at top of file

# SERIALIZATION: Convert Python object to binary format
data = {"name": "Groot", "age": 100}  # Dictionary to serialize
with open("data.pkl", "wb") as f:     # Open file in write-binary mode
    pickle.dump(data, f)              # Serialize and write to file

# DESERIALIZATION: Convert binary format back to Python object
with open("data.pkl", "rb") as f:     # Open file in read-binary mode
    loaded = pickle.load(f)           # Read and deserialize from file

print("Loaded from pickle:", loaded)  # Should be identical to original data

# ADVANCED PICKLING CONCEPTS:
# - pickle.dumps()/loads() for string serialization
# - Protocol versions (0-5) for compatibility/performance
# - Custom __getstate__/__setstate__ methods for complex objects

# ============================================================
# SUMMARY - KEY TAKEAWAYS
# ============================================================

"""
PERFORMANCE SUMMARY (Big O Notation):
- List access/append: O(1), insert/remove: O(n)
- Dict/Set access/insert/remove: O(1) average, O(n) worst case
- String operations: O(1) access, O(n) concatenation/search
- Tuple access: O(1), immutable so no modification operations

MEMORY CONSIDERATIONS:
- Lists: Dynamic arrays, can grow/shrink
- Tuples: Fixed size, more memory efficient than lists
- Sets/Dicts: Hash tables, extra memory for hash buckets
- Strings: Immutable, new object created for modifications

WHEN TO USE EACH DATA STRUCTURE:
- List: Ordered, mutable collection with duplicates
- Tuple: Ordered, immutable collection (coordinates, RGB values)
- Set: Unique elements, fast membership testing
- Dict: Key-value mapping, fast lookups by key
- String: Text data, immutable character sequences

BEST PRACTICES:
- Use appropriate data structure for the problem
- Consider time/space complexity for large datasets
- Avoid mutable default arguments in functions
- Use context managers (with statements) for file operations
- Handle edge cases (empty containers, division by zero)
"""

print("\nAll sections complete! 🎯 Try editing, breaking, and re-running code to learn deeply.")
print("Experiment with different data structures and observe their behavior!")