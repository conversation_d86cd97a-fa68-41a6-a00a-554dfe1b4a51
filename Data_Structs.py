"""
s = 'Hello, world! '
'world' in s                  # => True
len(s)                        # => 14
s.lower()                     # => 'hello, world! '
s.upper()                     # => 'HELLO, WORLD! '
s.strip()or s.strip('!')or... # => 'Hello, world!'
s.find('world')               # => 7 (-1 if not found)
s.replace('world', 'earth')   # => 'Hello, earth! '
s.replace('o', 'u')           # => 'Hellu, wurld! '
s = “h” + s[1:]				   # => 'hello, world! '
help( str )						# so much info!
"""

# str1 = 'Hello'
# str2 = 'world'
# '{}, {}!'.format(str1, str2)   
# # => 'Hello, world!'

# '{0}, {1}, {0}'.format('first', 'second')
# # => 'first, second, first'

# '{:.2f}'.format(2.71828) 
# # => 2.72

# age = 20
# name = ‘<PERSON>root’

# print(‘{0} was {1} years old when he got his first leaf.’.format(name, age))

# print(‘Why is {0} playing with fire!?! {0} Should know better!’).format(name))

# # try these for fun
# '{:.2f}'.format(age) 
# '{:.2f}'.format(name) 
# TA pet peeve: When you ask for help. Know the error code without looking at the screen.
# TA pet peeve #2: these commands help you ensure you do exactly as the directions say in the homework assignment.


# l = [1, 2, 3]
# l = [1, 2, 'three']
# l = [1, 2, [3, 4], [5]]

# l.append('six')    # => [1, 2, [3, 4], [5], 'six']

# row0 = [0, 1, 2]
# row1 = [3, 4, 5]
# row2 = [6, 7, 8]
# mat = [row0, row1, row2]

# mat[-1]        # => [6, 7, 8]
# mat[1:]        # => [[3, 4, 5], [6, 7, 8]]
# mat[1:][1:]    # => [[6, 7, 8]]

jagged = [[0], [1, 2], [3, 4, 5]]
0 in jagged                          # => False
# [0] in jagged                        # => True
# [1, 2] in jagged                     # => True
for row in jagged:		# => Will cover structures in more depth
    print('{} ({})'.format(row, len(row)))
#     # => [0] (1)
#     # => [1, 2] (2)
#     # => [3, 4, 5] (3)

string_number = "123"
integer_number = int(string_number)
print(integer_number)
print(type(integer_number))

# l = [1, 2, 3]
# l.append(4)      # => [1, 2, 3, 4]
# l.append(2)      # => [1, 2, 3, 4, 2]
# l.remove(2)      # => [1, 3, 4, 2] #try it again, then append a 2
# l.sort()         # => [1, 2, 3, 4]
# l.pop()          # => [1, 2, 3]
# l.pop(1)         # => [1, 3]
# l.reverse()      # => [3, 1]
# l.clear()        # => []

# 'a' + ('bc' * 2)     # => 'abcbc'
# [1] + ([2, 3] * 2)   # => [1, 2, 3, 2, 3]

# s = '1, 2, 3, 4, 5'    # => '1, 2, 3, 4, 5'
# myList = s.split(', ')      # => [‘1’, ‘2’, ‘3’, ‘4’, ‘5’]
# myList.remove(’3’)          # => [‘1’, ‘2’, ‘4’, ‘5’]
# s = '; '.join(myList)       # => '1; 2; 4; 5'

# list_int = [int(i) for i in myList]   # => [1, 2, 4, 5]
# # => learning for the pythonic way


# s = {1, 2, 3}
# s = {1, 2, 'three'}
# s = set([1, 2, 3, 3])    # => {1, 2, 3}
# s = set('Hello')         # => {'o', 'e', 'H', 'l'}
# s[0]                     # => TypeError




# s = set('mississippi')  # => {'p', 'm', 's', 'i'}

# len(s)          # => 4
# s.add('q')      # => {'i', 'q', 'm', 's', 'p'}
# s.remove('s')   # => {'i', 'q', 'm', 'p'}
# len(s)          # => 4

# 'm' in s        # => True



# s1 = set('Hello')    # => {'e', 'l', 'H', 'o'}
# s2 = set('world')    # => {'l', 'd', 'o', 'r', 'w'}
# # difference
# s1 - s2              # => {'H', 'e'}
# s2 - s1              # => {'w', 'r', 'd'}
# # union
# s1 | s2              # => {'H', 'e', 'l', 'w', 'd', 'o', 'r'}
# # intersection
# s1 & s2              # => {'o', 'l'}
# # symmetric difference
# s1 ^ s2              # => {'H', 'e', 'w', 'r', 'd'}


# s1 = {1, 2, 4}    
# s1.add(6)		 # sets have add / remove methods
# len(s1)         # len, min, max, sum built-in methods may work


# 3 in s1              # => False

# s2 = {1, 4, 5, 2, 6}
# s2.issuperset(s1)    # => True
# s1.issubset(s2)      # => True

# s3 = {1, 4, 2}
# s1 == s3			  # => True (for sets but not lists)


# a = dict(one=1, two=2)
# b = {'one': 1, 'two': 2}
# a == b                   # => True

# grades = {'Groot': [93, 87], 'Cassidy': [100, 94]}

# grades['Groot']      # => [93, 87]
# grades['John']        # => KeyError

# type(grades['Groot'])        # => lists
# (lists are mutable!)
# grades['Groot'][0] = 100 # => {'Groot': [100, 87],'Cassidy': [100, 94]}


# grades = {'Groot': [93, 87], 'Cassidy': [100, 94]}

# len(grades)           # => 2
# del grades['Groot']  # => {'Cassidy': [100, 94]}
# len(grades)           # => 1

# grades.keys()    # => dict_keys(['Cassidy'])
# grades.values()  # => dict_values([[100, 94]])
# grades.items()   # => dict_items([('Cassidy', [100, 94])])


# grades = {'Groot': [93, 87], 'Cassidy': [100, 94]}

# for name in grades:
#     print(name)    # => 'Groot'
#                    # => 'Cassidy'
# for grade in grades.values():
#     print(grade)   # => [93, 87]
#                    # => [100, 94]
                   
                   
# grades['Groot'] = [93, 87]  # Add Groot back to dict

# for name, grade in grades.items():
#     print('{}: {}'.format(name, grade))
#     # => Groot: [93, 87]
#     # => Cassidy: [100, 94]


s = set('Banana')    # => {'e', 'l', 'H', 'o'}
print(s)
s.add('q')		 # sets have add / remove methods
s.remove('B')	 # => {'e', 'l', 'o', 'q'}
print(s)
s.clear()		 # => set()