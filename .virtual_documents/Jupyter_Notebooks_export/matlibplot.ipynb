


# This line configures matplotlib to show figures embedded in the notebook, 
# instead of opening a new window for each figure. More about that later. 
# If you are using an old version of IPython, try using '%pylab inline' instead.
%matplotlib inline








import matplotlib
import matplotlib.pyplot as plt





import numpy as np








x = np.linspace(0, 5, 10)
y = x ** 2





fig = plt.figure()
axes = fig.add_axes([0, 0, 1, 1])  # Plot size as [left, botth, width, height] as a fraction from 0 to 1
axes.plot(x, y);





fig = plt.figure()
axes = fig.add_axes([0, 0, 1, 1])
axes.plot(x, y);
axes.set_xlabel('x');
axes.set_ylabel('y');
axes.set_title('y=x^2');





matplotlib.rcParams.update({'font.size': 18, 'font.family': 'STIXGeneral', 'mathtext.fontset': 'stix'})





fig = plt.figure()
axes = fig.add_axes([0, 0, 1, 1])
axes.plot(x, y);
axes.set_xlabel('x');
axes.set_ylabel('y');
axes.set_title('y=x^2');





fig = plt.figure()
axes = fig.add_axes([0, 0, 1, 1])
axes.plot(x, y);
axes.set_xlabel(r'$x$');  # the r prefix to the string indicates that Python shouldn't do any formatting on this string
                          # e.g. \n remains as the characters '\' and 'n' as opposed to turning into a newline character
axes.set_ylabel(r'$y$');
axes.set_title(r'$y=x^2$');





fig = plt.figure()
axes = fig.add_axes([0, 0, 1, 1])
axes.plot(x, y, label=r'$y=x^2$');
axes.set_xlabel(r'$x$');
axes.set_ylabel(r'$y$');
axes.set_title('Graph Title');
axes.legend();








fig, axes = plt.subplots(nrows=1, ncols=2, figsize=(10, 4))  # 2 rows and 1 column with a larger-than-default figure size
                                                             # to accomodate the two figures
axes[0].plot(x, x ** 2);
axes[0].set_title('Graph 1');

axes[1].plot(x, x ** 3);
axes[1].set_title('Graph 2');





fig, axes = plt.subplots()  # default is to create a single set of axes
axes.plot(x, x ** 2, label=r'$y=x^2$');
axes.plot(x, x ** 3, label=r'$y=x^3$');
axes.set_title('Graph Title');
axes.legend();





fig, axes = plt.subplots(1, 2, figsize=(10, 4))

axes[0].plot(x, x ** 2);
axes[0].plot(x, x ** 3);
axes[0].axis('tight');
axes[0].set_title('Tight');

axes[1].plot(x, x ** 2);
axes[1].plot(x, x ** 3);
axes[1].set_ylim([0, 60]);
axes[1].set_xlim([2, 5]);
axes[1].set_title('Custom');





fig, axes = plt.subplots(figsize=(10, 4))

axes.plot(x, x ** 2);
axes.plot(x, x ** 3);

axes.set_xticks([1, 2, 3, 4, 5]);
axes.set_xticklabels([r'$\alpha$', r'$\beta$', r'$\gamma$', r'$\delta$', r'$\epsilon$']);

yticks = [0, 50, 100, 150]
axes.set_yticks(yticks);
axes.set_yticklabels(["${:.1f}$".format(y) for y in yticks]);





fig, axes = plt.subplots(figsize=(12, 6))

axes.plot(x, x+1, color="blue", linewidth=0.25)
axes.plot(x, x+2, color="blue", linewidth=0.50)
axes.plot(x, x+3, color="blue", linewidth=1.00)
axes.plot(x, x+4, color="blue", linewidth=2.00)

# possible linestype options ‘-‘, ‘--’, ‘-.’, ‘:’, ‘steps’
axes.plot(x, x+5, color="red", lw=2, linestyle='-')
axes.plot(x, x+6, color="red", lw=2, ls='-.')
axes.plot(x, x+7, color="red", lw=2, ls=':')

# custom dash
line, = axes.plot(x, x+8, color="black", lw=1.50)
line.set_dashes([5, 10, 15, 10])  # format: line length, space length, ...

# possible marker symbols: marker = '+', 'o', '*', 's', ',', '.', '1', '2', '3', '4', ...
axes.plot(x, x+ 9, color="green", lw=2, ls='--', marker='+')
axes.plot(x, x+10, color="green", lw=2, ls='--', marker='o')
axes.plot(x, x+11, color="green", lw=2, ls='--', marker='s')
axes.plot(x, x+12, color="green", lw=2, ls='--', marker='1')

# marker size and color
axes.plot(x, x+13, color="purple", lw=1, ls='-', marker='o', markersize=2)
axes.plot(x, x+14, color="purple", lw=1, ls='-', marker='o', markersize=4)
axes.plot(x, x+15, color="purple", lw=1, ls='-', marker='o', markersize=8, markerfacecolor="red")
axes.plot(x, x+16, color="purple", lw=1, ls='-', marker='s', markersize=8, 
          markerfacecolor="yellow", markeredgewidth=2, markeredgecolor="blue");








xx = np.linspace(-0.75, 1., 100)
n = np.array([0, 1, 2, 3, 4, 5])
fig, axes = plt.subplots(1, 4, figsize=(14,3))

axes[0].scatter(xx, xx + 0.25 * np.random.randn(len(xx)))
axes[0].set_title('Scatter')

axes[1].step(n, n ** 2, lw=2)
axes[1].set_title('Step')

axes[2].bar(n, n ** 2, align="center", width=0.5, alpha=0.5)
axes[2].set_title('Bar')

axes[3].fill_between(x, x ** 2, x ** 3, color="green", alpha=0.5);
axes[3].set_title('Fill Between');


n = np.random.randn(100000)
fig, axes = plt.subplots(1, 2, figsize=(12,4))

axes[0].hist(n)
axes[0].set_title('Default histogram')
axes[0].set_xlim((min(n), max(n)))

axes[1].hist(n, cumulative=True, bins=50)
axes[1].set_title('Cumulative detailed histogram')
axes[1].set_xlim((min(n), max(n)));


alpha = 0.7
phi_ext = 2 * np.pi * 0.5

def flux_qubit_potential(phi_m, phi_p):
    return 2 + alpha - 2 * np.cos(phi_p) * np.cos(phi_m) - alpha * np.cos(phi_ext - 2*phi_p)

phi_m = np.linspace(0, 2*np.pi, 100)
phi_p = np.linspace(0, 2*np.pi, 100)
X,Y = np.meshgrid(phi_p, phi_m)
Z = flux_qubit_potential(X, Y).T

fig, ax = plt.subplots()

p = ax.pcolor(X/(2*np.pi), Y/(2*np.pi), Z, cmap=matplotlib.cm.RdBu, vmin=abs(Z).min(), vmax=abs(Z).max())
cb = fig.colorbar(p, ax=ax)


from mpl_toolkits.mplot3d.axes3d import Axes3D

fig = plt.figure(figsize=(14,6))

# `ax` is a 3D-aware axis instance because of the projection='3d' keyword argument to add_subplot
axes = fig.add_subplot(1, 2, 1, projection='3d')

p = axes.plot_surface(X, Y, Z, rstride=4, cstride=4, linewidth=0)

# surface_plot with color grading and color bar
axes = fig.add_subplot(1, 2, 2, projection='3d')
p = axes.plot_surface(X, Y, Z, rstride=1, cstride=1, cmap=matplotlib.cm.coolwarm, linewidth=0, antialiased=False)
cb = fig.colorbar(p, shrink=0.5)






