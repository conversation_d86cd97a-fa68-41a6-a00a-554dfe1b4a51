{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Test Notebook - VSCode Setup\n", "\n", "This notebook tests if your environment is set up correctly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1: Basic Python\n", "print(\"Hello from VSCode Jupyter!\")\n", "print(f\"Python version check...\")\n", "\n", "import sys\n", "print(f\"Python executable: {sys.executable}\")\n", "print(f\"Python version: {sys.version}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 2: <PERSON><PERSON><PERSON> numpy\n", "try:\n", "    import numpy as np\n", "    print(f\"✅ NumPy imported successfully! Version: {np.__version__}\")\n", "    \n", "    # Create a simple array\n", "    arr = np.array([1, 2, 3, 4, 5])\n", "    print(f\"Sample array: {arr}\")\n", "    print(f\"Array sum: {arr.sum()}\")\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Error importing numpy: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 3: Import pandas\n", "try:\n", "    import pandas as pd\n", "    print(f\"✅ Pandas imported successfully! Version: {pd.__version__}\")\n", "    \n", "    # Create a simple DataFrame\n", "    df = pd.DataFrame({\n", "        'Name': ['<PERSON>', '<PERSON>', '<PERSON>'],\n", "        'Age': [25, 30, 35],\n", "        'City': ['New York', 'London', 'Tokyo']\n", "    })\n", "    print(\"Sample DataFrame:\")\n", "    print(df)\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Error importing pandas: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 4: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    import matplotlib\n", "    print(f\"✅ Matplotlib imported successfully! Version: {matplotlib.__version__}\")\n", "    \n", "    # Create a simple plot\n", "    x = [1, 2, 3, 4, 5]\n", "    y = [2, 4, 6, 8, 10]\n", "    \n", "    plt.figure(figsize=(8, 4))\n", "    plt.plot(x, y, 'bo-', label='Sample Data')\n", "    plt.title('Test Plot - VSC<PERSON> Jupy<PERSON>')\n", "    plt.xlabel('X values')\n", "    plt.ylabel('Y values')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.show()\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Error importing matplotlib: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "If all the cells above run successfully with ✅ checkmarks, your environment is set up correctly!\n", "\n", "### Next Steps:\n", "1. Open your original notebooks: `numpy.ipynb`, `pandas.ipynb`, `matlibplot.ipynb`\n", "2. Make sure the same kernel/interpreter is selected\n", "3. Clear all outputs and re-run the cells\n", "\n", "### Troubleshooting:\n", "- If you see ❌ errors, check that you've selected the correct Python interpreter\n", "- The interpreter should be: `/Users/<USER>/Downloads/01 HW Code/02 Fall '25/ME 369P/00 Class Practice/369_1/bin/python`"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}