{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# numpy <PERSON>l"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["numpy is a library used to build multidimensional arrays and perform linear algebra operations. numpy calls dimensions of the matrix _axes_ and the number of axes _rank_."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initializing Arrays"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We begin by importing the numpy module."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'numpy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'numpy'"]}], "source": ["import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The simplest way to create a numpy array is to convert a Python array. Note that the type of a numpy array is different than that off a Python array and provides significantly more functionality. Types are deduced based on the values passed in through the Python array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3, 4])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["np.array([1, 2, 3, 4])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('int32')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1, 3, 3, 4])\n", "a.dtype"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('float64')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["b = np.array([1.2, 3.4, 5.6])\n", "b.dtype"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another simple way to create a new numpy array of a certain size is to initialize it by dimension directly. This can be achieved using `zeros`, `ones`, or some other size-based initialization method, which accept a tuple argument to specify the dimensions. `zeros` will zero-initialize the array and `ones` will one-initialize the array. There are also ways to initlalize with random numbers, identity matrix, etc."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0., 0., 0.],\n", "       [0., 0., 0., 0.],\n", "       [0., 0., 0., 0.]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["np.zeros((3, 4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1., 1., 1.],\n", "       [1., 1., 1., 1.],\n", "       [1., 1., 1., 1.]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones((3, 4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1., 1., 1.],\n", "       [1., 1., 1., 1.],\n", "       [1., 1., 1., 1.]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["np.empty((3, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another way to initialize a numpy array is using the `arange` or `linspace` methods. `arange` accepts the start and end of an interval as well as a step size (generaally used with integers). `linspace` accepts the start and end of an interval as well as the number of elements (generally used with floating point numbers). `arange` includes the start value and excludes the end value of the interval. `linspace` includes both ends of the interval by default, but can be set to exclude the end."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([10, 15, 20, 25])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["np.arange(10, 30, 5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.  , 0.25, 0.5 , 0.75, 1.  , 1.25, 1.5 , 1.75, 2.  ])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["np.linspace(0, 2, 9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Operating on arrays"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Common operations, such as addition, subtraction, exponentiation, etc., are executed element by element on arrays. numpy also provides some functions, such as `sin`, which operate on arrays elementwise."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([20, 29, 38, 47])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([20, 30, 40, 50])\n", "b = np.arange(4)  # implicitly starts at 0\n", "a - b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 4, 9], dtype=int32)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["b ** 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.00000000e+00,  7.81831482e-01,  9.74927912e-01,  4.33883739e-01,\n", "       -4.33883739e-01, -9.74927912e-01, -7.81831482e-01, -2.44929360e-16])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.linspace(0, 2 * np.pi, 8)\n", "np.sin(a)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Naturally, n<PERSON><PERSON> also provides some operations on matrices. The `*` operator does elementwise multiplication of two matrices, and the `dot` method does a matrix multiplication (i.e. repeated dot products to return a matrix multiplication)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2, 0],\n", "       [0, 4]])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([[1, 1],\n", "              [0, 1]])\n", "b = np.array([[2, 0],\n", "              [3, 4]])\n", "a * b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[5, 4],\n", "       [3, 4]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["np.dot(a, b)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are also functions that operate elementwise by default, but that can accept an `axis` parameter to change the behavior for multidimensional arrays. For example, selecting the `sum` operation to work along the 0th axis (columns), will produce a 1D array instead of a scalar value."]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["575"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([[0, 1,  2,  3],\n", "              [4, 514,  6,  7],\n", "              [8, 9, 10, 11]])\n", "np.sum(a)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([12, 15, 18, 21])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(a, axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 6, 22, 38])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(a, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1.5, 5.5, 9.5])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["np.mean(a, axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Shape manipulation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["numpy allows for slicing similar to Python's native slicing, but extended to support multiple dimensions. First let's look at the single dimension slicing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = np.arange(10) ** 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0,  1,  4,  9, 16, 25, 36, 49, 64, 81], dtype=int32)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["a[2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 4,  9, 16], dtype=int32)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["a[2:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([81, 64, 49, 36, 25, 16,  9,  4,  1,  0], dtype=int32)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["a[::-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The same sort of slicing can be used for multi-dimension arrays, where the slices for each dimension are separated by commas."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = np.fromfunction(lambda r, c: r + 2 * c, (5, 4), dtype=int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0,  2,  4,  6],\n", "       [ 1,  3,  5,  7],\n", "       [ 2,  4,  6,  8],\n", "       [ 3,  5,  7,  9],\n", "       [ 4,  6,  8, 10]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["b[2,3]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 3, 4, 5, 6])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["b[:,1]  # pull out the entirety of each row, and then from each row pull out the 2nd column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([3, 4])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["b[1:3,1] # pull out the entirety of the 2nd and 3rd row, and then from each of those pull out the 2nd column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 7,  9],\n", "       [ 8, 10]])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["b[-2:,-2:]  # pull out the bottom right 2x2 matrix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Additionally, numpy can reshape arrays. Reshaping refers to restructuring the same data into different dimensions. We first create an array of dimensions 3x4 filled with random integers between 0 and 9, inclusive. The `shape` method prints out the dimensions of the array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(3, 4)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.floor(10 * np.random.random((3, 4)))\n", "a.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `ravel` method can be used to flatten the array into a single dimension. Note that `ravel` does not modify the original array, but instead returns a flattened copy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([7., 6., 1., 6., 2., 3., 4., 7., 8., 4., 9., 7.])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["a.ravel()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also change the dimensions using the `reshape` method. The `reshape` method also returns a copy instead of modifying the original array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[7., 6.],\n", "       [1., 6.],\n", "       [2., 3.],\n", "       [4., 7.],\n", "       [8., 4.],\n", "       [9., 7.]])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["a.reshape(6, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since transposing is a common operation, n<PERSON>y provides an attribute `T` that contains the transposed array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[7., 1., 2., 4., 8., 9.],\n", "       [6., 6., 3., 7., 4., 7.]])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["a.reshape(6, 2).T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Arrays can also be stacked, provided their dimensions are agreeable. Stacking arrays places one array either to the on top of or on the side of another array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = np.floor(10 * np.random.random((2, 2)))\n", "b = np.floor(10 * np.random.random((2, 2)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0.],\n", "       [1., 7.]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 4.],\n", "       [5., 8.]])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0.],\n", "       [1., 7.],\n", "       [1., 4.],\n", "       [5., 8.]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["np.vstack((a, b))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0., 1., 4.],\n", "       [1., 7., 5., 8.]])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["np.hstack((a, b))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Linear algebra"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Working with arrays is useful and convenient with numpy, but much of the power comes from using its linear algebra library (and the fact that many analytic libraries support numpy types).\n", "\n", "First we need to import the linear algebra library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy.linalg as la"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To start off simple, you can use numpy to compute inner (dot) and cross products of vectors. These methods are in the standard numpy library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["32"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1, 2, 3])\n", "b = np.array([4, 5, 6])\n", "np.inner(a, b)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 0, 1])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([1, 0, 0])\n", "b = np.array([0, 1, 0])\n", "np.cross(a, b)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["n<PERSON><PERSON> can also compute the norm of a vector or matrix. The `norm` method is in the linear algebra library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.0"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([3, 4])\n", "la.norm(a)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["numpy can also be used to compute eigenvalues and eigenvectors using the `eig` method."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = np.diag((1, 2, 3))\n", "w, v = la.eig(a)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1., 2., 3.])"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["w"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0., 0.],\n", "       [0., 1., 0.],\n", "       [0., 0., 1.]])"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["v"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, numpy can solve linear matrix equations. For example, we can solve for $x$ and $y$ in the system of equations $3x+y=9$ and $x+2y=8$ using numpy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2., 3.])"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.array([[3, 1], [1, 2]])\n", "b = np.array([9, 8])\n", "la.solve(a, b)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* [numpy documentation](https://docs.scipy.org/doc/numpy-1.14.0/reference/)"]}], "metadata": {"kernelspec": {"display_name": "369_1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}