{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# pandas Tutorial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas is a Python library used for data analysis. pandas integrates well with numpy, matplotlib, and Jupyter notebooks. In some sense, pandas is similar to numpy, but pandas is much more geared toward data analysis while numpy is geared toward mathematical operations.\n", "\n", "We begin by importing the relevant modules."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'numpy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'numpy'"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading in data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas allows you to work with data using what's called a DataFrame, which is a grid of data. The rows of the grid correspond to each data entry and the columns of the grid, called Series, correspond to a specific type of data. A DataFrame can, in some sense, be thought of as an Excel spreadsheet filled with data.\n", "\n", "One way to load data is through numpy arrays."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Col1</th>\n", "      <th>Col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Row1</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Row2</th>\n", "      <td>3</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Col1 Col2\n", "Row1    1    2\n", "Row2    3    4"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["np_data = np.array([[    '', 'Col1', 'Col2'],\n", "                    ['Row1',      1,     2],\n", "                    ['Row2',      3,     4]])\n", "pd.DataFrame(data=np_data[1:,1:], index=np_data[1:,0], columns=np_data[0,1:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas can also work with timestamps. We'll create use pandas to create a range of dates and then numpy to create some random data. We'll use the dates as the first series and the random data as the next four series."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates = pd.date_range('20180101', periods=6)\n", "np_data = np.random.randn(6, 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["DatetimeIndex(['2018-01-01', '2018-01-02', '2018-01-03', '2018-01-04',\n", "               '2018-01-05', '2018-01-06'],\n", "              dtype='datetime64[ns]', freq='D')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.13432983, -1.86697794,  1.08399005, -0.96044401],\n", "       [ 1.42996944,  0.04464219, -0.97735293,  1.18167031],\n", "       [-2.10525045,  0.47846596,  0.08161497,  0.45512167],\n", "       [ 1.3060748 , -0.23422331, -0.9022194 ,  0.70541381],\n", "       [ 0.28655008, -1.18384601, -0.86415828,  1.41419018],\n", "       [-0.23025546, -1.20583164,  0.47957073,  0.30154855]])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["np_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2018-01-01</th>\n", "      <td>-0.134330</td>\n", "      <td>-1.866978</td>\n", "      <td>1.083990</td>\n", "      <td>-0.960444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-02</th>\n", "      <td>1.429969</td>\n", "      <td>0.044642</td>\n", "      <td>-0.977353</td>\n", "      <td>1.181670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-03</th>\n", "      <td>-2.105250</td>\n", "      <td>0.478466</td>\n", "      <td>0.081615</td>\n", "      <td>0.455122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-04</th>\n", "      <td>1.306075</td>\n", "      <td>-0.234223</td>\n", "      <td>-0.902219</td>\n", "      <td>0.705414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-05</th>\n", "      <td>0.286550</td>\n", "      <td>-1.183846</td>\n", "      <td>-0.864158</td>\n", "      <td>1.414190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-06</th>\n", "      <td>-0.230255</td>\n", "      <td>-1.205832</td>\n", "      <td>0.479571</td>\n", "      <td>0.301549</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   A         B         C         D\n", "2018-01-01 -0.134330 -1.866978  1.083990 -0.960444\n", "2018-01-02  1.429969  0.044642 -0.977353  1.181670\n", "2018-01-03 -2.105250  0.478466  0.081615  0.455122\n", "2018-01-04  1.306075 -0.234223 -0.902219  0.705414\n", "2018-01-05  0.286550 -1.183846 -0.864158  1.414190\n", "2018-01-06 -0.230255 -1.205832  0.479571  0.301549"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(np_data, index=dates, columns=list('ABCD'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Generally, you will be loading data from some data source. We'll load a csv file containing game review data from IGN. The `head` method just shows the top 5 rows. Since this data set is much larger, we only want to see a snippet of it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>score_phrase</th>\n", "      <th>title</th>\n", "      <th>url</th>\n", "      <th>platform</th>\n", "      <th>score</th>\n", "      <th>genre</th>\n", "      <th>editors_choice</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita</td>\n", "      <td>/games/littlebigplanet-vita/vita-98907</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita -- Marvel Super Hero E...</td>\n", "      <td>/games/littlebigplanet-ps-vita-marvel-super-he...</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>Great</td>\n", "      <td>Splice: Tree of Life</td>\n", "      <td>/games/splice/ipad-141070</td>\n", "      <td>iPad</td>\n", "      <td>8.5</td>\n", "      <td>Puzzle</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/xbox-360-128182</td>\n", "      <td>Xbox 360</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/ps3-128181</td>\n", "      <td>PlayStation 3</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0 score_phrase                                              title  \\\n", "0           0      Amazing                            LittleBigPlanet PS Vita   \n", "1           1      Amazing  LittleBigPlanet PS Vita -- Marvel Super Hero E...   \n", "2           2        Great                               Splice: Tree of Life   \n", "3           3        Great                                             NHL 13   \n", "4           4        Great                                             NHL 13   \n", "\n", "                                                 url          platform  score  \\\n", "0             /games/littlebigplanet-vita/vita-98907  PlayStation Vita    9.0   \n", "1  /games/littlebigplanet-ps-vita-marvel-super-he...  PlayStation Vita    9.0   \n", "2                          /games/splice/ipad-141070              iPad    8.5   \n", "3                      /games/nhl-13/xbox-360-128182          Xbox 360    8.5   \n", "4                           /games/nhl-13/ps3-128181     PlayStation 3    8.5   \n", "\n", "        genre editors_choice  release_year  release_month  release_day  \n", "0  Platformer              Y          2012              9           12  \n", "1  Platformer              Y          2012              9           12  \n", "2      Puzzle              N          2012              9           12  \n", "3      Sports              N          2012              9           11  \n", "4      Sports              N          2012              9           11  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews = pd.read_csv('ign.csv')\n", "reviews.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similar to numpy, we can see the size of our data using the `shape` method."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(18625, 11)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Indexing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As mentioned previously, pandas is similar to numpy in some respects. One of those is accessing sections of our DataFrame, which is very similar to accessing sub-arrays using numpy.\n", "\n", "We can emulate the behavior of the `head` method by using the `iloc` method, which works the same way as numpy slicing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>score_phrase</th>\n", "      <th>title</th>\n", "      <th>url</th>\n", "      <th>platform</th>\n", "      <th>score</th>\n", "      <th>genre</th>\n", "      <th>editors_choice</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita</td>\n", "      <td>/games/littlebigplanet-vita/vita-98907</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita -- Marvel Super Hero E...</td>\n", "      <td>/games/littlebigplanet-ps-vita-marvel-super-he...</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>Great</td>\n", "      <td>Splice: Tree of Life</td>\n", "      <td>/games/splice/ipad-141070</td>\n", "      <td>iPad</td>\n", "      <td>8.5</td>\n", "      <td>Puzzle</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/xbox-360-128182</td>\n", "      <td>Xbox 360</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/ps3-128181</td>\n", "      <td>PlayStation 3</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0 score_phrase                                              title  \\\n", "0           0      Amazing                            LittleBigPlanet PS Vita   \n", "1           1      Amazing  LittleBigPlanet PS Vita -- Marvel Super Hero E...   \n", "2           2        Great                               Splice: Tree of Life   \n", "3           3        Great                                             NHL 13   \n", "4           4        Great                                             NHL 13   \n", "\n", "                                                 url          platform  score  \\\n", "0             /games/littlebigplanet-vita/vita-98907  PlayStation Vita    9.0   \n", "1  /games/littlebigplanet-ps-vita-marvel-super-he...  PlayStation Vita    9.0   \n", "2                          /games/splice/ipad-141070              iPad    8.5   \n", "3                      /games/nhl-13/xbox-360-128182          Xbox 360    8.5   \n", "4                           /games/nhl-13/ps3-128181     PlayStation 3    8.5   \n", "\n", "        genre editors_choice  release_year  release_month  release_day  \n", "0  Platformer              Y          2012              9           12  \n", "1  Platformer              Y          2012              9           12  \n", "2      Puzzle              N          2012              9           12  \n", "3      Sports              N          2012              9           11  \n", "4      Sports              N          2012              9           11  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.iloc[0:5,:]  # access the first 5 rows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    9.0\n", "1    9.0\n", "2    8.5\n", "3    8.5\n", "4    8.5\n", "Name: score, dtype: float64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.iloc[0:5,5]  # access the score column of the first 5 rows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since the first series in the dataset is a duplicate of our index, let's remove it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>score_phrase</th>\n", "      <th>title</th>\n", "      <th>url</th>\n", "      <th>platform</th>\n", "      <th>score</th>\n", "      <th>genre</th>\n", "      <th>editors_choice</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita</td>\n", "      <td>/games/littlebigplanet-vita/vita-98907</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita -- Marvel Super Hero E...</td>\n", "      <td>/games/littlebigplanet-ps-vita-marvel-super-he...</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Great</td>\n", "      <td>Splice: Tree of Life</td>\n", "      <td>/games/splice/ipad-141070</td>\n", "      <td>iPad</td>\n", "      <td>8.5</td>\n", "      <td>Puzzle</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/xbox-360-128182</td>\n", "      <td>Xbox 360</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/ps3-128181</td>\n", "      <td>PlayStation 3</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  score_phrase                                              title  \\\n", "0      Amazing                            LittleBigPlanet PS Vita   \n", "1      Amazing  LittleBigPlanet PS Vita -- Marvel Super Hero E...   \n", "2        Great                               Splice: Tree of Life   \n", "3        Great                                             NHL 13   \n", "4        Great                                             NHL 13   \n", "\n", "                                                 url          platform  score  \\\n", "0             /games/littlebigplanet-vita/vita-98907  PlayStation Vita    9.0   \n", "1  /games/littlebigplanet-ps-vita-marvel-super-he...  PlayStation Vita    9.0   \n", "2                          /games/splice/ipad-141070              iPad    8.5   \n", "3                      /games/nhl-13/xbox-360-128182          Xbox 360    8.5   \n", "4                           /games/nhl-13/ps3-128181     PlayStation 3    8.5   \n", "\n", "        genre editors_choice  release_year  release_month  release_day  \n", "0  Platformer              Y          2012              9           12  \n", "1  Platformer              Y          2012              9           12  \n", "2      Puzzle              N          2012              9           12  \n", "3      Sports              N          2012              9           11  \n", "4      Sports              N          2012              9           11  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews = reviews.iloc[:,1:]\n", "reviews.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While this sort of indexing works well for matrices, one thing that sets pandas apart from numpy is that row and series labels can be used for addressing. To use labels, use the `loc` method instead of `iloc`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    9.0\n", "1    9.0\n", "2    8.5\n", "3    8.5\n", "4    8.5\n", "5    7.0\n", "Name: score, dtype: float64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.loc[0:5,'score']  # similar to the previous example, but indexing by name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see all the available columns by accessing the `columns` attribute."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['score_phrase', 'title', 'url', 'platform', 'score', 'genre',\n", "       'editors_choice', 'release_year', 'release_month', 'release_day'],\n", "      dtype='object')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also access multiple columns by passing in a list to `loc`. The order of the output will be the same as the order we request."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>score</th>\n", "      <th>title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>7.0</td>\n", "      <td>Total War Battles: Shogun</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3.0</td>\n", "      <td>Double Dragon: Neon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>9.0</td>\n", "      <td>Guild Wars 2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3.0</td>\n", "      <td>Double Dragon: Neon</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>7.0</td>\n", "      <td>Total War Battles: Shogun</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>7.5</td>\n", "      <td>Tekken Tag Tournament 2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    score                      title\n", "5     7.0  Total War Battles: Shogun\n", "6     3.0        Double Dragon: Neon\n", "7     9.0               Guild Wars 2\n", "8     3.0        Double Dragon: Neon\n", "9     7.0  Total War Battles: Shogun\n", "10    7.5    Tekken Tag Tournament 2"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.loc[5:10, ['score', 'title']]  # access the score and title from the second set of 5 rows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, we can access an entire series (or multiple) by directly indexing our DataFrame."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    9.0\n", "1    9.0\n", "2    8.5\n", "3    8.5\n", "4    8.5\n", "Name: score, dtype: float64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews['score'].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Computation on DataFrames"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas can be used for more than just data manipulation. For example, it provides methods such as `mean`, `median`, `std`, and several others to perform calculations on the data set.\n", "\n", "We can take the mean of scores like so."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["6.950459060402666"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews['score'].mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also take the mean of all of the numerical series if we don't only select the score series."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["score               6.950459\n", "release_year     2006.515329\n", "release_month       7.138470\n", "release_day        15.603866\n", "dtype: float64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another useful tool is to see if series are correlated to one another. pandas provides the `corr` method to do that."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>score</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>score</th>\n", "      <td>1.000000</td>\n", "      <td>0.062716</td>\n", "      <td>0.007632</td>\n", "      <td>0.020079</td>\n", "    </tr>\n", "    <tr>\n", "      <th>release_year</th>\n", "      <td>0.062716</td>\n", "      <td>1.000000</td>\n", "      <td>-0.115515</td>\n", "      <td>0.016867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>release_month</th>\n", "      <td>0.007632</td>\n", "      <td>-0.115515</td>\n", "      <td>1.000000</td>\n", "      <td>-0.067964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>release_day</th>\n", "      <td>0.020079</td>\n", "      <td>0.016867</td>\n", "      <td>-0.067964</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  score  release_year  release_month  release_day\n", "score          1.000000      0.062716       0.007632     0.020079\n", "release_year   0.062716      1.000000      -0.115515     0.016867\n", "release_month  0.007632     -0.115515       1.000000    -0.067964\n", "release_day    0.020079      0.016867      -0.067964     1.000000"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews.corr()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since the values are all far from 1 (except for the diagonal), we can conclude that the series are not correlated. This may be useful, for example, in determining if games have been getting better over the years (i.e. `release_year` and `score` have some sort of correlation)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filtering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas makes filtering easy. All you need to do is specify the predicate and pandas will apply it to the DataFrame or Series!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     True\n", "1     True\n", "2     True\n", "3     True\n", "4     True\n", "5    False\n", "6    False\n", "7     True\n", "8    False\n", "9    False\n", "Name: score, dtype: bool"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["score_filter = reviews['score'] > 7\n", "score_filter.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A predicate can then be used to index into the DataFrame."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_reviews = reviews[score_filter]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>score_phrase</th>\n", "      <th>title</th>\n", "      <th>url</th>\n", "      <th>platform</th>\n", "      <th>score</th>\n", "      <th>genre</th>\n", "      <th>editors_choice</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita</td>\n", "      <td>/games/littlebigplanet-vita/vita-98907</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Amazing</td>\n", "      <td>LittleBigPlanet PS Vita -- Marvel Super Hero E...</td>\n", "      <td>/games/littlebigplanet-ps-vita-marvel-super-he...</td>\n", "      <td>PlayStation Vita</td>\n", "      <td>9.0</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Great</td>\n", "      <td>Splice: Tree of Life</td>\n", "      <td>/games/splice/ipad-141070</td>\n", "      <td>iPad</td>\n", "      <td>8.5</td>\n", "      <td>Puzzle</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/xbox-360-128182</td>\n", "      <td>Xbox 360</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Great</td>\n", "      <td>NHL 13</td>\n", "      <td>/games/nhl-13/ps3-128181</td>\n", "      <td>PlayStation 3</td>\n", "      <td>8.5</td>\n", "      <td>Sports</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Amazing</td>\n", "      <td>Guild Wars 2</td>\n", "      <td>/games/guild-wars-2/pc-896298</td>\n", "      <td>PC</td>\n", "      <td>9.0</td>\n", "      <td>RPG</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Good</td>\n", "      <td>Tekken Tag Tournament 2</td>\n", "      <td>/games/tekken-tag-tournament-2/ps3-124584</td>\n", "      <td>PlayStation 3</td>\n", "      <td>7.5</td>\n", "      <td>Fighting</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Good</td>\n", "      <td>Tekken Tag Tournament 2</td>\n", "      <td>/games/tekken-tag-tournament-2/xbox-360-124581</td>\n", "      <td>Xbox 360</td>\n", "      <td>7.5</td>\n", "      <td>Fighting</td>\n", "      <td>N</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Amazing</td>\n", "      <td><PERSON> of the Ninja</td>\n", "      <td>/games/mark-of-the-ninja-135615/xbox-360-129276</td>\n", "      <td>Xbox 360</td>\n", "      <td>9.0</td>\n", "      <td>Action, Adventure</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Amazing</td>\n", "      <td><PERSON> of the Ninja</td>\n", "      <td>/games/mark-of-the-ninja-135615/pc-143761</td>\n", "      <td>PC</td>\n", "      <td>9.0</td>\n", "      <td>Action, Adventure</td>\n", "      <td>Y</td>\n", "      <td>2012</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   score_phrase                                              title  \\\n", "0       Amazing                            LittleBigPlanet PS Vita   \n", "1       Amazing  LittleBigPlanet PS Vita -- Marvel Super Hero E...   \n", "2         Great                               Splice: Tree of Life   \n", "3         Great                                             NHL 13   \n", "4         Great                                             NHL 13   \n", "7       Amazing                                       Guild Wars 2   \n", "10         Good                            Tekken Tag Tournament 2   \n", "11         Good                            Tekken Tag Tournament 2   \n", "13      Amazing                                  Mark of the Ninja   \n", "14      <PERSON>                                  Mark of the Ninja   \n", "\n", "                                                  url          platform  \\\n", "0              /games/littlebigplanet-vita/vita-98907  PlayStation Vita   \n", "1   /games/littlebigplanet-ps-vita-marvel-super-he...  PlayStation Vita   \n", "2                           /games/splice/ipad-141070              iPad   \n", "3                       /games/nhl-13/xbox-360-128182          Xbox 360   \n", "4                            /games/nhl-13/ps3-128181     PlayStation 3   \n", "7                       /games/guild-wars-2/pc-896298                PC   \n", "10          /games/tekken-tag-tournament-2/ps3-124584     PlayStation 3   \n", "11     /games/tekken-tag-tournament-2/xbox-360-124581          Xbox 360   \n", "13    /games/mark-of-the-ninja-135615/xbox-360-129276          Xbox 360   \n", "14          /games/mark-of-the-ninja-135615/pc-143761                PC   \n", "\n", "    score              genre editors_choice  release_year  release_month  \\\n", "0     9.0         Platformer              Y          2012              9   \n", "1     9.0         Platformer              Y          2012              9   \n", "2     8.5             Puzzle              N          2012              9   \n", "3     8.5             Sports              N          2012              9   \n", "4     8.5             Sports              N          2012              9   \n", "7     9.0                RPG              Y          2012              9   \n", "10    7.5           Fighting              N          2012              9   \n", "11    7.5           Fighting              N          2012              9   \n", "13    9.0  Action, Adventure              Y          2012              9   \n", "14    9.0  Action, Adventure              Y          2012              9   \n", "\n", "    release_day  \n", "0            12  \n", "1            12  \n", "2            12  \n", "3            11  \n", "4            11  \n", "7            11  \n", "10           11  \n", "11           11  \n", "13            7  \n", "14            7  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_reviews.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also combine filters just as easily."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>score_phrase</th>\n", "      <th>title</th>\n", "      <th>url</th>\n", "      <th>platform</th>\n", "      <th>score</th>\n", "      <th>genre</th>\n", "      <th>editors_choice</th>\n", "      <th>release_year</th>\n", "      <th>release_month</th>\n", "      <th>release_day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>17137</th>\n", "      <td>Amazing</td>\n", "      <td>Gone Home</td>\n", "      <td>/games/gone-home/xbox-one-20014361</td>\n", "      <td>Xbox One</td>\n", "      <td>9.5</td>\n", "      <td>Simulation</td>\n", "      <td>Y</td>\n", "      <td>2013</td>\n", "      <td>8</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17197</th>\n", "      <td>Amazing</td>\n", "      <td>Rayman Legends</td>\n", "      <td>/games/rayman-legends/xbox-one-20008449</td>\n", "      <td>Xbox One</td>\n", "      <td>9.5</td>\n", "      <td>Platformer</td>\n", "      <td>Y</td>\n", "      <td>2013</td>\n", "      <td>8</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17295</th>\n", "      <td>Amazing</td>\n", "      <td>LEGO Marvel Super Heroes</td>\n", "      <td>/games/lego-marvel-super-heroes/xbox-one-20000826</td>\n", "      <td>Xbox One</td>\n", "      <td>9.0</td>\n", "      <td>Action</td>\n", "      <td>Y</td>\n", "      <td>2013</td>\n", "      <td>10</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17313</th>\n", "      <td>Great</td>\n", "      <td>Dead Rising 3</td>\n", "      <td>/games/dead-rising-3/xbox-one-124306</td>\n", "      <td>Xbox One</td>\n", "      <td>8.3</td>\n", "      <td>Action</td>\n", "      <td>N</td>\n", "      <td>2013</td>\n", "      <td>11</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17317</th>\n", "      <td>Great</td>\n", "      <td>Killer Instinct</td>\n", "      <td>/games/killer-instinct-2013/xbox-one-20000538</td>\n", "      <td>Xbox One</td>\n", "      <td>8.4</td>\n", "      <td>Fighting</td>\n", "      <td>N</td>\n", "      <td>2013</td>\n", "      <td>11</td>\n", "      <td>18</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      score_phrase                     title  \\\n", "17137      Amazing                 Gone Home   \n", "17197      Amazing            Rayman Legends   \n", "17295      Amazing  LEGO Marvel Super Heroes   \n", "17313        Great             Dead Rising 3   \n", "17317        Great           Killer Instinct   \n", "\n", "                                                     url  platform  score  \\\n", "17137                 /games/gone-home/xbox-one-20014361  Xbox One    9.5   \n", "17197            /games/rayman-legends/xbox-one-20008449  Xbox One    9.5   \n", "17295  /games/lego-marvel-super-heroes/xbox-one-20000826  Xbox One    9.0   \n", "17313               /games/dead-rising-3/xbox-one-124306  Xbox One    8.3   \n", "17317      /games/killer-instinct-2013/xbox-one-20000538  Xbox One    8.4   \n", "\n", "            genre editors_choice  release_year  release_month  release_day  \n", "17137  Simulation              Y          2013              8           15  \n", "17197  Platformer              Y          2013              8           26  \n", "17295      Action              Y          2013             10           22  \n", "17313      Action              N          2013             11           18  \n", "17317    Fighting              N          2013             11           18  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["xbox_one_filter = (reviews['score'] > 7) & (reviews['platform'] == 'Xbox One')\n", "filtered_reviews = reviews[xbox_one_filter]\n", "filtered_reviews.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plotting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Plotting is extremely easy with pandas!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<matplotlib.figure.Figure at 0x228812c5278>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xbox_one_filter = reviews['platform'] == 'Xbox One'\n", "reviews[xbox_one_filter]['score'].plot(kind='hist');"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<matplotlib.figure.Figure at 0x22880e610b8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ps4_filter = reviews['platform'] == 'PlayStation 4'\n", "reviews[ps4_filter]['score'].plot(kind='hist');"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks like based on IGN reviews, the PS4 has a much higher quality to content ratio than the Xbox One!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## References"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* [10-minute tutorial](https://pandas.pydata.org/pandas-docs/stable/10min.html)\n", "* [<PERSON><PERSON><PERSON>' tutorial](https://www.datacamp.com/community/tutorials/pandas-tutorial-dataframe-python)\n", "* [Dataquest tutorial](https://www.dataquest.io/blog/pandas-python-tutorial/)"]}], "metadata": {"kernelspec": {"display_name": "369_1", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}