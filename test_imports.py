#!/usr/bin/env python3
"""
Test script to verify Python imports work correctly in VSCode
"""

import sys
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path[:3]}...")  # Show first 3 paths
print()

# Test numpy import
try:
    import numpy as np
    print(f"✅ NumPy imported successfully!")
    print(f"   Version: {np.__version__}")
    print(f"   Location: {np.__file__}")
    
    # Quick test
    arr = np.array([1, 2, 3])
    print(f"   Test array: {arr}")
    print()
    
except ImportError as e:
    print(f"❌ NumPy import failed: {e}")
    print()

# Test other common packages
packages_to_test = ['pandas', 'matplotlib']

for package in packages_to_test:
    try:
        module = __import__(package)
        print(f"✅ {package} imported successfully! Version: {module.__version__}")
    except ImportError:
        print(f"❌ {package} not available")

print("\n" + "="*50)
print("If you see ✅ for numpy, your environment is working!")
print("If you see ❌, check your Python interpreter in VSCode.")
