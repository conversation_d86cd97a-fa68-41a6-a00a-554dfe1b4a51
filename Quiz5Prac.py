# 🧠 Python Practice Quiz: Enumerate, Dictionaries, String Formatting, and Functions
# Based on your class examples — with answers and brief explanations
import matplotlib.pyplot as plt
# ---------------------------------------------------------------

# ============================================================
# Question 1: Enumerate Basics
# ============================================================
myRoster = ['Groot', 'Rocket', 'Hulk', 'Cap', 'Strange', 'Cap Marvel', 'She-Hulk', 'Agnes']

for index, member in enumerate(myRoster):
    print(index, ": ", member, sep="")

# ✅ Answer:
# Prints:
# 0: Groot
# 1: Rocket
# ...
# 7: Agnes
# 🧩 enumerate() starts counting from 0 by default.

# ============================================================
# Question 2: Enumerate with start parameter
# ============================================================
for index, member in enumerate(myRoster, start=1):
    print(index, ": ", member, sep="")

# ✅ Answer:
# Starts counting from 1 instead of 0.
# 1: Groot, 2: Rocket, ..., 8: Agnes

# ============================================================
# Question 3: Enumerate on Dictionaries
# ============================================================
myRoster = {'Groot': 103, 'Rocket': -23, 'Cap Marvel': 34, 'She-Hulk': 45, 'Agnes': 31}

for index, member in enumerate(myRoster, start=1):
    print(index, member, myRoster[member])

# ✅ Answer:
# Prints index, key, and value in dictionary order (insertion order in Python 3.7+).
# Example:
# 1 Groot 103
# 2 Rocket -23
# ...

# ============================================================
# Question 4: Enumerate + zip()
# ============================================================
myRoster = ['Groot', 'Rocket', 'Hulk', 'Cap', 'Strange', 'Cap Marvel', 'She-Hulk', 'Agnes']
nMovies = [12, 12, 5, 7, 3005, 2, 0, 0]

print(dict(zip(myRoster, nMovies)))

for index, (hero, count) in enumerate(zip(myRoster, nMovies)):
    print(f"{index}: {hero} was in {count} movies.")

# ✅ Answer:
# zip() combines the two lists into pairs.
# enumerate() adds an index to each pair.
# If one list is shorter, zip stops at the shortest.

# ============================================================
# Question 5: enumerate() + list()
# ============================================================
numbers = [17, 22, 96, 100, 101, -23]
letters = 'I am Groot!'

indexed_list = list(enumerate(numbers))
print(indexed_list)
# ✅ Answer: [(0,17), (1,22), (2,96), (3,100), (4,101), (5,-23)]

indexed_list2 = list(enumerate(letters))
print(indexed_list2)
# ✅ Answer: [('I', etc.)] Each character gets indexed.

# ============================================================
# Question 6: Nested Enumerate
# ============================================================
indexed_list3 = list(enumerate(indexed_list2))
print(indexed_list3)

# ✅ Answer:
# Each item of indexed_list2 gets its own index, e.g.
# [(0, (0,'I')), (1, (1,' ')), (2, (2,'a')) ...]
# So you have nested tuples.

# ============================================================
# Question 7: String Formatting (%)
# ============================================================
name = "Groot"
age = 15
print("Hello, %s." % name)
print("Hello, %s. You are %s." % (name, age))

first_name = "Iam"
last_name = "Groot"
profession = "Guardian"
affiliation = "The Avengers"
print("Hello, %s %s. You are %s. You are a %s. You were a member of %s." %
      (first_name, last_name, age, profession, affiliation))

# ✅ Answer:
# % formatting uses placeholders like %s.
# Each %s is replaced in order by the provided values.

# ============================================================
# Question 8: String Formatting (.format)
# ============================================================
print("Hello, {}. You are {} years old".format(name, age))

print(("Hello, {first_name} {last_name}. You are {age}. "
       + "You are a {profession}. You were a member of {affiliation}.").format(
           first_name=first_name,
           last_name=last_name,
           age=age,
           profession=profession,
           affiliation=affiliation))

# ✅ Answer:
# .format() can use either positional {} or named placeholders {name=value}.

# ============================================================
# Question 9: f-Strings
# ============================================================
print(f"Hello, {name}. You are {age}")
print(f"{2 * 100}")
print(f"{name.lower()}")

# ✅ Answer:
# f-strings evaluate expressions directly.
# Output:
# Hello, Groot. You are 15
# 200
# groot

# ============================================================
# Question 10: Class with __str__
# ============================================================
class Hero:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def __str__(self):
        return f"{self.name} is {self.age}."

new_hero = Hero("Iam Groot", "12,345")
print(f"{new_hero}")

newer_hero = Hero("Iam Groot", 15)
print(f"{newer_hero}")

# ✅ Answer:
# When using f"{obj}", Python automatically calls obj.__str__().
# Output:
# Iam Groot is 12,345.
# Iam Groot is 15.

# ============================================================
# Question 11: Function with Dictionary Lookup
# ============================================================
def fnc(x):
    return {
        'one': 1,
        'two': 2,
        'three': 3,
        'four': 4,
    }.get(x, 5)

print(fnc("one"))    # ✅ 1
print(fnc("three"))  # ✅ 3
print(fnc("seven"))  # ✅ 5 (default value)

# 🧩 .get(key, default) returns default if key not found.

# ============================================================
# Question 12: Number-based Dictionary Lookup
# ============================================================
def fnc(x):
    return {
        1: "up",
        2: "down",
        3: "left",
        4: "right",
    }.get(x, "stay")

print(fnc(1))     # ✅ "up"
print(fnc(3))     # ✅ "left"
print(fnc(-100))  # ✅ "stay"

# ============================================================
# Question 13: Multiple Dictionaries as Arguments
# ============================================================
d = {1: "up", 2: "down", 3: "left", 4: "right"}
e = {1: "jump", 2: "duck", 3: "left", 4: "right"}

def fnc(d, x):
    return d.get(x, "stay")

print(fnc(d, 1))     # ✅ up
print(fnc(d, 3))     # ✅ left
print(fnc(e, 2))     # ✅ duck
print(fnc(e, -100))  # ✅ stay

# ============================================================
# Question 14: Bonus System Function
# ============================================================
import os
print(os.getlogin())

# ✅ Answer:
# Prints the username of the person currently logged into the OS session.

# ============================================================
# Question 15: Logical & Range Practice
# ============================================================
total = 0
for i in range(1, 5 + 4 * 12, 4):
    total = total + ((0 / 2 ** i) + (0 / 2 ** (i + 1)) + (1 / 2 ** (i + 3)) + (1 / 2 ** (i + 4)))
    print(i, total)

# ✅ Answer:
# range(1, 53, 4) → i = 1, 5, 9, ..., 49
# total adds small fractions each time (mainly from the (1 / 2^(i+3)) and (1 / 2^(i+4)) terms).
#===========================================================
#!/usr/bin/env python3
"""
Practice Quiz — Python + Matplotlib + String Formatting + Enumerate
Author: Lambert Ike
Course: Application Programming for Engineers
University of Texas at Austin
"""

# ============================================================
# 🧩 MATPLOTLIB PRACTICE PROBLEMS
# ============================================================

# Q1 — Basic Line Plot
# Free Response:
# Why does the x-axis run from 0–3 and the y-axis from 1–4
# when only one list is passed to plt.plot()?

import matplotlib.pyplot as plt

plt.plot([1, 2, 3, 4])
plt.ylabel('some numbers')
plt.show()


# ------------------------------------------------------------
# Q2 — X vs Y Plot + Format String
# Free Response:
# What does 'ro' represent in the plot function?
# What does axis() do?

plt.plot([1, 2, 3, 4], [1, 4, 9, 16], 'ro')
plt.axis((0, 6, 0, 20))
plt.show()


# ------------------------------------------------------------
# Q3 — Multiple Lines in One Plot
# Free Response:
# Explain what each format string means and how many datasets are plotted.

import numpy as np

t = np.arange(0., 5., 0.2)
plt.plot(t, t, 'r--', t, t**2, 'bs', t, t**3, 'g^')
plt.show()


# ------------------------------------------------------------
# Q4 — Plotting from a Dictionary
# Free Response:
# Explain how c='c' and s='d' affect the scatter plot.

data = {
    'a': np.arange(50),
    'c': np.random.randint(0, 50, 50),
    'd': np.random.randn(50)
}
data['b'] = data['a'] + 10 * np.random.randn(50)
data['d'] = np.abs(data['d']) * 100

plt.scatter('a', 'b', c='c', s='d', data=data)
plt.xlabel('entry a')
plt.ylabel('entry b')
plt.show()


# ------------------------------------------------------------
# Q5 — Categorical Plotting
# Free Response:
# What does plt.subplot(131) mean?
# Why can categorical (string) values be used for the x-axis?

names = ['group_a', 'group_b', 'group_c']
values = [1, 10, 100]

plt.figure(figsize=(9, 3))
plt.subplot(131)
plt.bar(names, values)
plt.subplot(132)
plt.scatter(names, values)
plt.subplot(133)
plt.plot(names, values)
plt.suptitle('Categorical Plotting')
plt.show()


# ------------------------------------------------------------
# Q6 — Logarithmic and Nonlinear Axes
# Free Response:
# Describe the difference between linear, log, symlog, and logit scales.

np.random.seed(19680801)
y = np.random.normal(loc=0.5, scale=0.4, size=1000)
y = y[(y > 0) & (y < 1)]
y.sort()
x = np.arange(len(y))

plt.figure()
plt.subplot(221)
plt.plot(x, y)
plt.yscale('linear')
plt.title('linear')

plt.subplot(222)
plt.plot(x, y)
plt.yscale('log')
plt.title('log')

plt.subplot(223)
plt.plot(x, y - y.mean())
plt.yscale('symlog', linthresh=0.01)
plt.title('symlog')

plt.subplot(224)
plt.plot(x, y)
plt.yscale('logit')
plt.title('logit')

plt.tight_layout()
plt.show()


# ============================================================
# 📚 PYTHON CONCEPTS — ENUMERATE, ZIP, STR.FORMAT, DICTIONARY
# ============================================================

# Q1 — Enumerate Example
# Free Response:
# Use enumerate() to print heroes starting at index 10.
# Explain what enumerate() returns.

myRoster = ['Groot', 'Rocket', 'Hulk', 'Cap', 'Strange', 'Cap Marvel', 'She-Hulk', 'Agnes']

for index, member in enumerate(myRoster, start=10):
    print(index, ": ", member, sep="")


# ------------------------------------------------------------
# Q2 — ZIP Example
# Free Response:
# Use zip() to pair names and scores, make a dictionary, and print.
# What happens if the lists differ in length?

names = ['Groot', 'Rocket', 'Cap']
scores = [10, 20, 30]

zipped_dict = dict(zip(names, scores))
print(zipped_dict)


# ------------------------------------------------------------
# Q3 — String Formatting Methods
# Free Response:
# Print “Hello, Tony Stark. You are 48. You are an Avenger.”
# once using %, once using .format(), once using f-string.

name = "Tony Stark"
age = 48
team = "Avenger"

print("Hello, %s. You are %s. You are an %s." % (name, age, team))
print("Hello, {}. You are {}. You are an {}.".format(name, age, team))
print(f"Hello, {name}. You are {age}. You are an {team}.")


# ------------------------------------------------------------
# Q4 — Dictionary with Enumerate
# Free Response:
# Print the index, hero name, and value using enumerate().
# Explain how dictionary order is preserved in Python 3.7+.

heroes = {'Groot': 103, 'Rocket': -23, 'Cap Marvel': 34}

for index, hero in enumerate(heroes, start=1):
    print(index, hero, heroes[hero])


# ------------------------------------------------------------
# Q5 — __str__() Method
# Free Response:
# Explain what __str__() does.
# What will print(Hero("Groot", 15)) output?

class Hero:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def __str__(self):
        return f"{self.name} is {self.age} years old."

print(Hero("Groot", 15))


# ------------------------------------------------------------
# Q6 — Dictionary Get() and Defaults
# Free Response:
# Explain how .get() works when the key is not found.

def fnc(x):
    return {
        'one': 1,
        'two': 2,
        'three': 3,
        'four': 4,
    }.get(x, 5)

print(fnc("one"))
print(fnc("seven"))  # default value


# ============================================================
# ✅ END OF PRACTICE QUIZ
# ============================================================
print("\n-- End of Practice Quiz --")